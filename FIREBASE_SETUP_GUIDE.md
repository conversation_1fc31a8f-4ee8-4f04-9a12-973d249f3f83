# Firebase Real-time Implementation Guide

## Overview
এই guide আপনাকে cost-effective Firebase Realtime Database setup করতে সাহায্য করবে যা Socket.IO এর বিকল্প হিসেবে কাজ করবে।

## Prerequisites
- Firebase account
- Node.js project (already set up)
- Basic understanding of Firebase

## Step 1: Firebase Project Setup

### 1.1 Create Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Enter project name (e.g., "hifnf-realtime")
4. Enable Google Analytics (optional)
5. Create project

### 1.2 Enable Realtime Database
1. In Firebase Console, go to "Realtime Database"
2. Click "Create Database"
3. Choose location (closest to your users)
4. Start in **test mode** (we'll add security rules later)

### 1.3 Get Configuration
1. Go to Project Settings (gear icon)
2. Scroll to "Your apps" section
3. Click "Web" icon to add web app
4. Register app with nickname
5. Copy the configuration object

## Step 2: Environment Configuration

### 2.1 Add Environment Variables
Create or update your `.env.local` file:

```bash
# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_DATABASE_URL=https://your_project_id-default-rtdb.firebaseio.com/
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

# Optional: Firebase Emulator (for development)
NEXT_PUBLIC_USE_FIREBASE_EMULATOR=false
```

### 2.2 Verify Installation
The Firebase SDK should already be installed. Verify with:
```bash
npm list firebase
```

## Step 3: Security Rules Setup

### 3.1 Apply Security Rules
1. Go to Firebase Console → Realtime Database → Rules
2. Replace the default rules with the content from `firebase-database-rules.json`
3. Click "Publish"

### 3.2 Test Rules
Use Firebase Console's simulator to test rules with different user IDs.

## Step 4: Initialize Firebase Services

### 4.1 Update Your App Component
Add Firebase initialization to your main app component:

```typescript
// In your _app.tsx or main layout component
import { useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { messageService } from '@/lib/services/messageService';
import { initializeUsageMonitoring, onFirebaseUsageAlert } from '@/lib/firebase/usageMonitor';

export default function App({ Component, pageProps }) {
  const { data: session } = useSession();

  useEffect(() => {
    // Initialize Firebase services when user logs in
    if (session?.user?.id) {
      messageService.initializeFirebase(session.user.id);
      
      // Initialize usage monitoring
      initializeUsageMonitoring({
        dailyReads: 10000,    // Adjust based on your needs
        dailyWrites: 5000,    // Adjust based on your needs
        monthlyCost: 25,      // Your budget in USD
      });

      // Set up usage alerts
      const unsubscribe = onFirebaseUsageAlert((alert) => {
        console.warn('Firebase Usage Alert:', alert);
        // You can show toast notifications or send to monitoring service
      });

      return () => {
        unsubscribe();
        messageService.cleanupFirebase();
      };
    }
  }, [session?.user?.id]);

  return <Component {...pageProps} />;
}
```

## Step 5: Cost Optimization Setup

### 5.1 Enable Automatic Cleanup
Add to your app initialization:

```typescript
import { scheduleAutomaticCleanup } from '@/lib/firebase/dataCleanup';

// Schedule cleanup every 24 hours
const cleanupUnsubscribe = scheduleAutomaticCleanup(24, {
  maxMessageAge: 30 * 24 * 60 * 60 * 1000, // 30 days
  maxNotificationAge: 7 * 24 * 60 * 60 * 1000, // 7 days
  maxMessagesPerConversation: 100,
  maxNotificationsPerUser: 50,
  dryRun: false, // Set to true for testing
});
```

### 5.2 Monitor Usage
Create a monitoring dashboard:

```typescript
import { getFirebaseUsageStats } from '@/lib/firebase/usageMonitor';

function UsageDashboard() {
  const [stats, setStats] = useState(null);

  useEffect(() => {
    const interval = setInterval(() => {
      const currentStats = getFirebaseUsageStats();
      setStats(currentStats);
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, []);

  return (
    <div>
      <h3>Firebase Usage</h3>
      <p>Reads: {stats?.reads || 0}</p>
      <p>Writes: {stats?.writes || 0}</p>
      <p>Bandwidth: {((stats?.bandwidth || 0) / 1024 / 1024).toFixed(2)} MB</p>
    </div>
  );
}
```

## Step 6: Testing

### 6.1 Test Real-time Messaging
1. Open your app in two browser windows
2. Log in as different users
3. Send messages between users
4. Verify real-time delivery

### 6.2 Test Notifications
1. Like a post from one user
2. Check if notification appears for post owner
3. Verify notification count updates

### 6.3 Test Typing Indicators
1. Start typing in message input
2. Check if typing indicator appears for other user
3. Verify it disappears after stopping

## Step 7: Production Deployment

### 7.1 Update Security Rules
Ensure your security rules are production-ready and restrict access properly.

### 7.2 Set Usage Limits
Configure appropriate usage limits for production:

```typescript
initializeUsageMonitoring({
  dailyReads: 50000,     // Adjust based on user count
  dailyWrites: 20000,    // Adjust based on activity
  dailyBandwidth: 5 * 1024 * 1024 * 1024, // 5GB
  monthlyCost: 50,       // Your production budget
  concurrentConnections: 200,
});
```

### 7.3 Enable Monitoring
Set up alerts for production:

```typescript
onFirebaseUsageAlert((alert) => {
  // Send to your monitoring service (e.g., Sentry, DataDog)
  console.error('Production Firebase Alert:', alert);
  
  // Send email/Slack notification for critical alerts
  if (alert.severity === 'critical') {
    // Your alerting logic here
  }
});
```

## Cost Optimization Tips

### 1. **Minimize Listeners**
- Only listen to active conversations
- Remove listeners when components unmount
- Use connection pooling

### 2. **Optimize Data Structure**
- Keep messages flat
- Avoid deep nesting
- Use efficient queries

### 3. **Implement Caching**
- Cache frequently accessed data
- Use local storage for offline support
- Implement smart refresh strategies

### 4. **Monitor Usage**
- Set up alerts for usage spikes
- Regular cleanup of old data
- Monitor cost trends

### 5. **Use Free Tier Efficiently**
- Stay within free limits when possible
- Optimize for read/write ratios
- Use compression for large data

## Troubleshooting

### Common Issues

1. **Permission Denied**
   - Check security rules
   - Verify user authentication
   - Ensure proper user ID format

2. **Connection Issues**
   - Check network connectivity
   - Verify Firebase configuration
   - Check browser console for errors

3. **High Usage**
   - Review data structure efficiency
   - Check for memory leaks in listeners
   - Optimize query patterns

4. **Real-time Not Working**
   - Verify Firebase initialization
   - Check listener setup
   - Test with Firebase Console

### Debug Mode
Enable debug logging:

```typescript
// Add to your Firebase config
if (process.env.NODE_ENV === 'development') {
  // Enable Firebase debug logging
  console.log('Firebase debug mode enabled');
}
```

## Support

For issues or questions:
1. Check Firebase Console for errors
2. Review browser console logs
3. Test with Firebase emulator
4. Check security rules in Firebase Console

## Next Steps

After successful setup:
1. Monitor usage patterns
2. Optimize based on real data
3. Consider upgrading to Blaze plan if needed
4. Implement additional real-time features
5. Set up proper monitoring and alerting

Remember: Start with conservative limits and scale up based on actual usage patterns!
