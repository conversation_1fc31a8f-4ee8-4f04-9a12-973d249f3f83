/**
 * Firebase Data Cleanup Utilities
 * 
 * Automated cleanup functions to minimize Firebase storage costs
 * and maintain optimal performance.
 */

import { database, isFirebaseConfigured, FIREBASE_SETTINGS, getUserPaths } from './config';
import { ref, get, remove, query, orderByChild, limitToFirst, set } from 'firebase/database';

export interface CleanupStats {
  messagesDeleted: number;
  notificationsDeleted: number;
  typingIndicatorsCleared: number;
  presenceDataCleaned: number;
  totalSizeReduced: number; // in bytes (estimated)
}

export interface CleanupOptions {
  maxMessageAge: number; // in milliseconds
  maxNotificationAge: number; // in milliseconds
  maxMessagesPerConversation: number;
  maxNotificationsPerUser: number;
  cleanupTypingIndicators: boolean;
  cleanupOfflinePresence: boolean;
  dryRun: boolean; // if true, only calculate what would be deleted
}

class FirebaseDataCleanup {
  private defaultOptions: CleanupOptions = {
    maxMessageAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    maxNotificationAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    maxMessagesPerConversation: FIREBASE_SETTINGS.MESSAGE_HISTORY_LIMIT * 2, // 100 messages
    maxNotificationsPerUser: FIREBASE_SETTINGS.NOTIFICATION_BATCH_SIZE * 5, // 50 notifications
    cleanupTypingIndicators: true,
    cleanupOfflinePresence: true,
    dryRun: false,
  };

  /**
   * Run comprehensive cleanup
   */
  public async runCleanup(options: Partial<CleanupOptions> = {}): Promise<CleanupStats> {
    if (!isFirebaseConfigured()) {
      throw new Error('Firebase not configured');
    }

    const cleanupOptions = { ...this.defaultOptions, ...options };
    const stats: CleanupStats = {
      messagesDeleted: 0,
      notificationsDeleted: 0,
      typingIndicatorsCleared: 0,
      presenceDataCleaned: 0,
      totalSizeReduced: 0,
    };

    console.log('🧹 Starting Firebase data cleanup...', {
      dryRun: cleanupOptions.dryRun,
      options: cleanupOptions,
    });

    try {
      // Clean up old messages
      const messageStats = await this.cleanupOldMessages(cleanupOptions);
      stats.messagesDeleted += messageStats.deleted;
      stats.totalSizeReduced += messageStats.sizeReduced;

      // Clean up old notifications
      const notificationStats = await this.cleanupOldNotifications(cleanupOptions);
      stats.notificationsDeleted += notificationStats.deleted;
      stats.totalSizeReduced += notificationStats.sizeReduced;

      // Clean up typing indicators
      if (cleanupOptions.cleanupTypingIndicators) {
        const typingStats = await this.cleanupTypingIndicators(cleanupOptions);
        stats.typingIndicatorsCleared += typingStats.cleared;
        stats.totalSizeReduced += typingStats.sizeReduced;
      }

      // Clean up offline presence data
      if (cleanupOptions.cleanupOfflinePresence) {
        const presenceStats = await this.cleanupOfflinePresence(cleanupOptions);
        stats.presenceDataCleaned += presenceStats.cleaned;
        stats.totalSizeReduced += presenceStats.sizeReduced;
      }

      console.log('🧹 Cleanup completed:', stats);
      return stats;
    } catch (error) {
      console.error('Error during cleanup:', error);
      throw error;
    }
  }

  /**
   * Clean up old messages from conversations
   */
  private async cleanupOldMessages(options: CleanupOptions): Promise<{ deleted: number; sizeReduced: number }> {
    const messagesRef = ref(database, 'messages');
    const snapshot = await get(messagesRef);
    
    if (!snapshot.exists()) {
      return { deleted: 0, sizeReduced: 0 };
    }

    let deleted = 0;
    let sizeReduced = 0;
    const now = Date.now();
    const cutoffTime = now - options.maxMessageAge;

    const conversations = snapshot.val();
    
    for (const [conversationId, messages] of Object.entries(conversations)) {
      if (!messages || typeof messages !== 'object') continue;

      const messageEntries = Object.entries(messages as Record<string, any>);
      
      // Sort by timestamp
      messageEntries.sort((a, b) => (a[1].timestamp || 0) - (b[1].timestamp || 0));

      // Delete old messages
      const oldMessages = messageEntries.filter(([_, message]) => 
        message.timestamp && message.timestamp < cutoffTime
      );

      // Delete excess messages (keep only maxMessagesPerConversation)
      const excessMessages = messageEntries.slice(0, -options.maxMessagesPerConversation);

      const messagesToDelete = [...oldMessages, ...excessMessages];

      for (const [messageId, message] of messagesToDelete) {
        if (!options.dryRun) {
          const messageRef = ref(database, `messages/${conversationId}/${messageId}`);
          await remove(messageRef);
        }
        
        deleted++;
        sizeReduced += this.estimateMessageSize(message);
      }
    }

    console.log(`🗑️ Messages cleanup: ${deleted} messages deleted, ~${Math.round(sizeReduced / 1024)}KB saved`);
    return { deleted, sizeReduced };
  }

  /**
   * Clean up old notifications
   */
  private async cleanupOldNotifications(options: CleanupOptions): Promise<{ deleted: number; sizeReduced: number }> {
    const notificationsRef = ref(database, 'notifications');
    const snapshot = await get(notificationsRef);
    
    if (!snapshot.exists()) {
      return { deleted: 0, sizeReduced: 0 };
    }

    let deleted = 0;
    let sizeReduced = 0;
    const now = Date.now();
    const cutoffTime = now - options.maxNotificationAge;

    const userNotifications = snapshot.val();
    
    for (const [userId, notifications] of Object.entries(userNotifications)) {
      if (!notifications || typeof notifications !== 'object') continue;

      const notificationEntries = Object.entries(notifications as Record<string, any>);
      
      // Sort by timestamp
      notificationEntries.sort((a, b) => (b[1].timestamp || 0) - (a[1].timestamp || 0));

      // Delete old notifications
      const oldNotifications = notificationEntries.filter(([_, notification]) => 
        notification.timestamp && notification.timestamp < cutoffTime
      );

      // Delete excess notifications (keep only maxNotificationsPerUser)
      const excessNotifications = notificationEntries.slice(options.maxNotificationsPerUser);

      const notificationsToDelete = [...oldNotifications, ...excessNotifications];

      for (const [notificationId, notification] of notificationsToDelete) {
        if (!options.dryRun) {
          const notificationRef = ref(database, `notifications/${userId}/${notificationId}`);
          await remove(notificationRef);
        }
        
        deleted++;
        sizeReduced += this.estimateNotificationSize(notification);
      }
    }

    console.log(`🗑️ Notifications cleanup: ${deleted} notifications deleted, ~${Math.round(sizeReduced / 1024)}KB saved`);
    return { deleted, sizeReduced };
  }

  /**
   * Clean up stale typing indicators
   */
  private async cleanupTypingIndicators(options: CleanupOptions): Promise<{ cleared: number; sizeReduced: number }> {
    const typingRef = ref(database, 'typing');
    const snapshot = await get(typingRef);
    
    if (!snapshot.exists()) {
      return { cleared: 0, sizeReduced: 0 };
    }

    let cleared = 0;
    let sizeReduced = 0;
    const now = Date.now();
    const staleThreshold = 5 * 60 * 1000; // 5 minutes

    const conversations = snapshot.val();
    
    for (const [conversationId, typingUsers] of Object.entries(conversations)) {
      if (!typingUsers || typeof typingUsers !== 'object') continue;

      for (const [userId, typingData] of Object.entries(typingUsers as Record<string, any>)) {
        if (typingData.timestamp && (now - typingData.timestamp) > staleThreshold) {
          if (!options.dryRun) {
            const typingUserRef = ref(database, `typing/${conversationId}/${userId}`);
            await remove(typingUserRef);
          }
          
          cleared++;
          sizeReduced += this.estimateTypingSize(typingData);
        }
      }
    }

    console.log(`🗑️ Typing indicators cleanup: ${cleared} indicators cleared, ~${Math.round(sizeReduced / 1024)}KB saved`);
    return { cleared, sizeReduced };
  }

  /**
   * Clean up offline presence data
   */
  private async cleanupOfflinePresence(options: CleanupOptions): Promise<{ cleaned: number; sizeReduced: number }> {
    const presenceRef = ref(database, 'presence');
    const snapshot = await get(presenceRef);
    
    if (!snapshot.exists()) {
      return { cleaned: 0, sizeReduced: 0 };
    }

    let cleaned = 0;
    let sizeReduced = 0;
    const now = Date.now();
    const offlineThreshold = 24 * 60 * 60 * 1000; // 24 hours

    const presenceData = snapshot.val();
    
    for (const [userId, presence] of Object.entries(presenceData)) {
      if (!presence || typeof presence !== 'object') continue;

      const presenceInfo = presence as any;
      
      // Clean up users who have been offline for more than threshold
      if (!presenceInfo.online && 
          presenceInfo.lastSeen && 
          (now - presenceInfo.lastSeen) > offlineThreshold) {
        
        if (!options.dryRun) {
          const userPresenceRef = ref(database, `presence/${userId}`);
          await remove(userPresenceRef);
        }
        
        cleaned++;
        sizeReduced += this.estimatePresenceSize(presenceInfo);
      }
    }

    console.log(`🗑️ Presence cleanup: ${cleaned} offline users cleaned, ~${Math.round(sizeReduced / 1024)}KB saved`);
    return { cleaned, sizeReduced };
  }

  /**
   * Schedule automatic cleanup
   */
  public scheduleCleanup(intervalHours: number = 24, options: Partial<CleanupOptions> = {}): () => void {
    const interval = setInterval(async () => {
      try {
        await this.runCleanup(options);
      } catch (error) {
        console.error('Scheduled cleanup failed:', error);
      }
    }, intervalHours * 60 * 60 * 1000);

    console.log(`⏰ Scheduled cleanup every ${intervalHours} hours`);
    
    return () => {
      clearInterval(interval);
      console.log('⏰ Scheduled cleanup cancelled');
    };
  }

  // Size estimation methods (approximate)
  private estimateMessageSize(message: any): number {
    return JSON.stringify(message).length * 2; // UTF-16 encoding
  }

  private estimateNotificationSize(notification: any): number {
    return JSON.stringify(notification).length * 2;
  }

  private estimateTypingSize(typing: any): number {
    return JSON.stringify(typing).length * 2;
  }

  private estimatePresenceSize(presence: any): number {
    return JSON.stringify(presence).length * 2;
  }
}

// Export singleton instance
export const firebaseDataCleanup = new FirebaseDataCleanup();

// Export for manual cleanup
export const runManualCleanup = (options?: Partial<CleanupOptions>) => 
  firebaseDataCleanup.runCleanup(options);

// Export for scheduling
export const scheduleAutomaticCleanup = (intervalHours?: number, options?: Partial<CleanupOptions>) =>
  firebaseDataCleanup.scheduleCleanup(intervalHours, options);
