/**
 * Firebase Real-time Notification Service
 * 
 * Cost-optimized notification system using Firebase Realtime Database.
 * Implements selective sync and efficient data structures.
 */

import { database, isFirebaseConfigured, FIREBASE_SETTINGS, getUserPaths } from './config';
import { connectionManager } from './connectionManager';
import { ref, push, set, serverTimestamp, get, remove } from 'firebase/database';

export interface FirebaseNotification {
  id: string;
  recipientId: string;
  type: string;
  title: string;
  message: string;
  senderId?: string;
  postId?: string;
  commentId?: string;
  messageId?: string;
  fanPageId?: string;
  groupId?: string;
  read: boolean;
  timestamp: number;
  metadata?: Record<string, any>;
}

export interface NotificationPreferences {
  messages: boolean;
  likes: boolean;
  comments: boolean;
  friendRequests: boolean;
  groupInvites: boolean;
  fanPageUpdates: boolean;
  emailNotifications: boolean;
  pushNotifications: boolean;
}

class FirebaseNotificationService {
  private currentUserId: string | null = null;
  private notificationListeners: Map<string, () => void> = new Map();

  /**
   * Initialize notification service for a user
   */
  public async initialize(userId: string): Promise<void> {
    if (!isFirebaseConfigured()) {
      console.warn('Firebase not configured, notification service disabled');
      return;
    }

    this.currentUserId = userId;
    await connectionManager.connectUser(userId);
    
    console.log(`🔔 Notification service initialized for user: ${userId}`);
  }

  /**
   * Send a real-time notification
   */
  public async sendNotification(notification: Omit<FirebaseNotification, 'id' | 'timestamp'>): Promise<{ success: boolean; notificationId?: string; error?: string }> {
    if (!isFirebaseConfigured()) {
      return { success: false, error: 'Firebase not configured' };
    }

    try {
      const userPaths = getUserPaths(notification.recipientId);
      const notificationsRef = ref(database, userPaths.notifications);
      
      const notificationData: Omit<FirebaseNotification, 'id'> = {
        ...notification,
        timestamp: Date.now(),
      };

      // Push notification to Firebase
      const newNotificationRef = push(notificationsRef);
      const notificationId = newNotificationRef.key!;

      await set(newNotificationRef, notificationData);

      // Clean up old notifications to save storage
      await this.cleanupOldNotifications(notification.recipientId);

      console.log(`🔔 Notification sent: ${notificationId} to ${notification.recipientId}`);
      return { success: true, notificationId };
    } catch (error) {
      console.error('Error sending notification:', error);
      return { success: false, error: 'Failed to send notification' };
    }
  }

  /**
   * Listen to real-time notifications for current user
   */
  public listenToNotifications(
    onNotification: (notifications: FirebaseNotification[]) => void,
    onError?: (error: string) => void
  ): () => void {
    if (!this.currentUserId || !isFirebaseConfigured()) {
      onError?.('Service not initialized');
      return () => {};
    }

    const userPaths = getUserPaths(this.currentUserId);
    const listenerId = `notifications_${this.currentUserId}`;

    // Listen to notifications with limit for cost optimization
    connectionManager.addListener(
      listenerId,
      userPaths.notifications,
      (data) => {
        if (data) {
          const notifications: FirebaseNotification[] = Object.entries(data)
            .map(([id, notificationData]: [string, any]) => ({
              id,
              ...notificationData,
            }))
            .sort((a, b) => b.timestamp - a.timestamp)
            .slice(0, FIREBASE_SETTINGS.NOTIFICATION_BATCH_SIZE * 2); // Keep more for better UX
          
          onNotification(notifications);
        } else {
          onNotification([]);
        }
      },
      { limitToLast: FIREBASE_SETTINGS.NOTIFICATION_BATCH_SIZE * 2 }
    );

    // Store cleanup function
    const cleanup = () => {
      connectionManager.removeListener(listenerId);
      this.notificationListeners.delete(listenerId);
    };

    this.notificationListeners.set(listenerId, cleanup);
    return cleanup;
  }

  /**
   * Mark notification as read
   */
  public async markAsRead(notificationId: string): Promise<void> {
    if (!this.currentUserId || !isFirebaseConfigured()) return;

    try {
      const userPaths = getUserPaths(this.currentUserId);
      const notificationRef = ref(database, `${userPaths.notifications}/${notificationId}`);
      
      // Get current notification data
      const snapshot = await get(notificationRef);
      const notificationData = snapshot.val();

      if (notificationData) {
        await set(notificationRef, {
          ...notificationData,
          read: true,
        });
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }

  /**
   * Mark all notifications as read
   */
  public async markAllAsRead(): Promise<void> {
    if (!this.currentUserId || !isFirebaseConfigured()) return;

    try {
      const userPaths = getUserPaths(this.currentUserId);
      const notificationsRef = ref(database, userPaths.notifications);
      
      const snapshot = await get(notificationsRef);
      const data = snapshot.val();

      if (data) {
        const updates: Record<string, any> = {};
        
        Object.entries(data).forEach(([id, notificationData]: [string, any]) => {
          if (!notificationData.read) {
            updates[`${userPaths.notifications}/${id}/read`] = true;
          }
        });

        if (Object.keys(updates).length > 0) {
          // Batch update for efficiency
          const promises = Object.entries(updates).map(([path, value]) =>
            set(ref(database, path), value)
          );
          
          await Promise.all(promises);
        }
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }

  /**
   * Delete a notification
   */
  public async deleteNotification(notificationId: string): Promise<void> {
    if (!this.currentUserId || !isFirebaseConfigured()) return;

    try {
      const userPaths = getUserPaths(this.currentUserId);
      const notificationRef = ref(database, `${userPaths.notifications}/${notificationId}`);
      
      await remove(notificationRef);
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  }

  /**
   * Get notification preferences
   */
  public async getPreferences(): Promise<NotificationPreferences> {
    if (!this.currentUserId || !isFirebaseConfigured()) {
      return this.getDefaultPreferences();
    }

    try {
      const preferencesRef = ref(database, `user_preferences/${this.currentUserId}/notifications`);
      const snapshot = await get(preferencesRef);
      const data = snapshot.val();

      return data ? { ...this.getDefaultPreferences(), ...data } : this.getDefaultPreferences();
    } catch (error) {
      console.error('Error getting notification preferences:', error);
      return this.getDefaultPreferences();
    }
  }

  /**
   * Update notification preferences
   */
  public async updatePreferences(preferences: Partial<NotificationPreferences>): Promise<void> {
    if (!this.currentUserId || !isFirebaseConfigured()) return;

    try {
      const preferencesRef = ref(database, `user_preferences/${this.currentUserId}/notifications`);
      const currentPreferences = await this.getPreferences();
      
      await set(preferencesRef, {
        ...currentPreferences,
        ...preferences,
      });
    } catch (error) {
      console.error('Error updating notification preferences:', error);
    }
  }

  /**
   * Get unread notification count
   */
  public async getUnreadCount(): Promise<number> {
    if (!this.currentUserId || !isFirebaseConfigured()) return 0;

    try {
      const userPaths = getUserPaths(this.currentUserId);
      const notificationsRef = ref(database, userPaths.notifications);
      
      const snapshot = await get(notificationsRef);
      const data = snapshot.val();

      if (!data) return 0;

      return Object.values(data).filter((notification: any) => !notification.read).length;
    } catch (error) {
      console.error('Error getting unread count:', error);
      return 0;
    }
  }

  /**
   * Send bulk notifications (for admin use)
   */
  public async sendBulkNotifications(
    recipientIds: string[],
    notification: Omit<FirebaseNotification, 'id' | 'recipientId' | 'timestamp'>
  ): Promise<{ success: boolean; sent: number; failed: number }> {
    if (!isFirebaseConfigured()) {
      return { success: false, sent: 0, failed: recipientIds.length };
    }

    let sent = 0;
    let failed = 0;

    // Process in batches to avoid overwhelming Firebase
    const batchSize = 10;
    for (let i = 0; i < recipientIds.length; i += batchSize) {
      const batch = recipientIds.slice(i, i + batchSize);
      
      const promises = batch.map(async (recipientId) => {
        try {
          await this.sendNotification({
            ...notification,
            recipientId,
          });
          sent++;
        } catch (error) {
          console.error(`Failed to send notification to ${recipientId}:`, error);
          failed++;
        }
      });

      await Promise.all(promises);
      
      // Small delay between batches to be respectful to Firebase
      if (i + batchSize < recipientIds.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return { success: sent > 0, sent, failed };
  }

  /**
   * Cleanup service
   */
  public cleanup(): void {
    // Remove all notification listeners
    this.notificationListeners.forEach(cleanup => cleanup());
    this.notificationListeners.clear();

    this.currentUserId = null;
    console.log('🔔 Notification service cleaned up');
  }

  // Private methods

  private getDefaultPreferences(): NotificationPreferences {
    return {
      messages: true,
      likes: true,
      comments: true,
      friendRequests: true,
      groupInvites: true,
      fanPageUpdates: true,
      emailNotifications: false,
      pushNotifications: true,
    };
  }

  private async cleanupOldNotifications(userId: string): Promise<void> {
    try {
      const userPaths = getUserPaths(userId);
      const notificationsRef = ref(database, userPaths.notifications);
      
      const snapshot = await get(notificationsRef);
      const data = snapshot.val();

      if (!data) return;

      const notifications = Object.entries(data).map(([id, notificationData]: [string, any]) => ({
        id,
        ...notificationData,
      }));

      // Keep only the most recent notifications
      const maxNotifications = FIREBASE_SETTINGS.NOTIFICATION_BATCH_SIZE * 5; // Keep 5x batch size
      if (notifications.length > maxNotifications) {
        const sortedNotifications = notifications.sort((a, b) => b.timestamp - a.timestamp);
        const toDelete = sortedNotifications.slice(maxNotifications);

        // Delete old notifications
        const deletePromises = toDelete.map(notification =>
          remove(ref(database, `${userPaths.notifications}/${notification.id}`))
        );

        await Promise.all(deletePromises);
        console.log(`🧹 Cleaned up ${toDelete.length} old notifications for user ${userId}`);
      }
    } catch (error) {
      console.error('Error cleaning up old notifications:', error);
    }
  }
}

// Export singleton instance
export const firebaseNotificationService = new FirebaseNotificationService();

// Export for debugging
export const getNotificationServiceStatus = () => ({
  isInitialized: firebaseNotificationService['currentUserId'] !== null,
  activeListeners: firebaseNotificationService['notificationListeners'].size,
  connectionStatus: connectionManager.getStatus(),
});
