interface NotificationData {
  id: string;
  type: string;
  recipientId: string;
  senderId: string;
  read: boolean;
  createdAt: string;
  sender?: {
    id: string;
    name: string;
    image: string;
  };
  postId?: string;
  commentId?: string;
  messageId?: string;
  friendshipId?: string;
  fanPageId?: string;
  fanPagePostId?: string;
  groupId?: string;
  eventId?: string;
  storeId?: string;
  productId?: string;
}

import { firebaseNotificationService } from '../firebase/notificationService';

// Real-time notification implementation using Firebase
export async function emitNotification(notification: NotificationData) {
  try {
    // Send via Firebase for real-time delivery
    const result = await firebaseNotificationService.sendNotification({
      recipientId: notification.recipientId,
      type: notification.type,
      title: getNotificationTitle(notification.type),
      message: getNotificationMessage(notification),
      senderId: notification.senderId,
      postId: notification.postId,
      commentId: notification.commentId,
      messageId: notification.messageId,
      fanPageId: notification.fanPageId,
      groupId: notification.groupId,
      read: false,
      metadata: {
        friendshipId: notification.friendshipId,
        fanPagePostId: notification.fanPagePostId,
        eventId: notification.eventId,
        storeId: notification.storeId,
        productId: notification.productId,
      },
    });

    if (result.success) {
      console.log('📡 Real-time notification sent:', notification.type);
    } else {
      console.error('Failed to send real-time notification:', result.error);
    }
  } catch (error) {
    console.error('Error sending real-time notification:', error);
  }
}

// Helper functions for notification content
function getNotificationTitle(type: string): string {
  const titles: Record<string, string> = {
    'like': 'New Like',
    'comment': 'New Comment',
    'message': 'New Message',
    'friend_request': 'Friend Request',
    'friend_accept': 'Friend Request Accepted',
    'fan_page_message': 'Page Message',
    'fan_page_reply': 'Page Reply',
    'group_invite': 'Group Invitation',
    'event_invite': 'Event Invitation',
    'store_follow': 'Store Follow',
    'product_like': 'Product Like',
  };

  return titles[type] || 'New Notification';
}

function getNotificationMessage(notification: NotificationData): string {
  const senderName = notification.sender?.name || 'Someone';

  switch (notification.type) {
    case 'like':
      return `${senderName} liked your post`;
    case 'comment':
      return `${senderName} commented on your post`;
    case 'message':
      return `${senderName} sent you a message`;
    case 'friend_request':
      return `${senderName} sent you a friend request`;
    case 'friend_accept':
      return `${senderName} accepted your friend request`;
    case 'fan_page_message':
      return `${senderName} sent a message to your page`;
    case 'fan_page_reply':
      return `Your page replied to ${senderName}`;
    case 'group_invite':
      return `${senderName} invited you to join a group`;
    case 'event_invite':
      return `${senderName} invited you to an event`;
    case 'store_follow':
      return `${senderName} started following your store`;
    case 'product_like':
      return `${senderName} liked your product`;
    default:
      return `${senderName} sent you a notification`;
  }
}

// Helper function to create notification data
export function createNotificationData(
  type: string,
  recipientId: string,
  senderId: string,
  options: {
    postId?: string;
    commentId?: string;
    messageId?: string;
    friendshipId?: string;
    fanPageId?: string;
    fanPagePostId?: string;
    groupId?: string;
    eventId?: string;
    storeId?: string;
    productId?: string;
    sender?: {
      id: string;
      name: string;
      image: string;
    };
  } = {}
): NotificationData {
  return {
    id: '', // Will be set by the database
    type,
    recipientId,
    senderId,
    read: false,
    createdAt: new Date().toISOString(),
    ...options,
  };
}
