import { toast } from "react-hot-toast";
import { firebaseMessageService, FirebaseMessage } from "../firebase/messageService";
import { firebaseNotificationService } from "../firebase/notificationService";

export interface MessageResponse {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
}

export interface SendMessageOptions {
  content: string;
  receiverId?: string;
  fanPageId?: string;
  isReply?: boolean;
  recipientId?: string;
}

export interface MessageStatus {
  id: string;
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  timestamp: Date;
  retryCount?: number;
}

class MessageService {
  private messageStatuses = new Map<string, MessageStatus>();
  private retryQueue: Array<{ id: string; options: SendMessageOptions }> = [];
  private maxRetries = 3;
  private isFirebaseInitialized = false;
  private currentUserId: string | null = null;

  // Initialize Firebase services
  async initializeFirebase(userId: string): Promise<void> {
    if (this.isFirebaseInitialized && this.currentUserId === userId) {
      return;
    }

    try {
      this.currentUserId = userId;
      await firebaseMessageService.initialize(userId);
      await firebaseNotificationService.initialize(userId);
      this.isFirebaseInitialized = true;
      console.log('🔥 Firebase services initialized for messaging');
    } catch (error) {
      console.error('Failed to initialize Firebase services:', error);
      this.isFirebaseInitialized = false;
    }
  }

  // Cleanup Firebase services
  cleanupFirebase(): void {
    if (this.isFirebaseInitialized) {
      firebaseMessageService.cleanup();
      firebaseNotificationService.cleanup();
      this.isFirebaseInitialized = false;
      this.currentUserId = null;
      console.log('🔥 Firebase services cleaned up');
    }
  }

  // Send user-to-user message
  async sendUserMessage(content: string, receiverId: string): Promise<MessageResponse> {
    const tempId = this.generateTempId();

    try {
      this.updateMessageStatus(tempId, 'sending');

      // Send via Firebase for real-time delivery (if available)
      if (this.isFirebaseInitialized) {
        const firebaseResult = await firebaseMessageService.sendMessage(receiverId, content);
        if (firebaseResult.success) {
          console.log('📱 Message sent via Firebase real-time');
        }
      }

      // Also send via traditional API for database persistence
      const response = await fetch("/api/messages", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          receiverId,
          content: content.trim(),
        }),
      });

      const result = await response.json();

      if (response.ok) {
        this.updateMessageStatus(tempId, 'sent');

        // Send real-time notification
        if (this.isFirebaseInitialized) {
          await firebaseNotificationService.sendNotification({
            recipientId: receiverId,
            type: 'message',
            title: 'New Message',
            message: content.length > 50 ? content.substring(0, 50) + '...' : content,
            senderId: this.currentUserId || undefined,
            read: false,
          });
        }

        return {
          success: true,
          data: result.data,
          message: "Message sent successfully"
        };
      } else {
        this.updateMessageStatus(tempId, 'failed');
        return {
          success: false,
          error: result.message || "Failed to send message"
        };
      }
    } catch (error) {
      this.updateMessageStatus(tempId, 'failed');
      this.addToRetryQueue(tempId, { content, receiverId });

      return {
        success: false,
        error: "Network error. Message will be retried."
      };
    }
  }

  // Send user-to-fanpage message
  async sendFanPageMessage(content: string, fanPageId: string): Promise<MessageResponse> {
    const tempId = this.generateTempId();
    
    try {
      this.updateMessageStatus(tempId, 'sending');
      
      const response = await fetch("/api/messages/fanpage", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          fanPageId,
          content: content.trim(),
        }),
      });

      const result = await response.json();

      if (response.ok) {
        this.updateMessageStatus(tempId, 'sent');
        return {
          success: true,
          data: result.data,
          message: "Message sent to page successfully"
        };
      } else {
        this.updateMessageStatus(tempId, 'failed');
        return {
          success: false,
          error: result.error || "Failed to send message to page"
        };
      }
    } catch (error) {
      this.updateMessageStatus(tempId, 'failed');
      this.addToRetryQueue(tempId, { content, fanPageId });
      
      return {
        success: false,
        error: "Network error. Message will be retried."
      };
    }
  }

  // Send fanpage-to-user reply
  async sendPageReply(content: string, pageId: string, recipientId: string): Promise<MessageResponse> {
    const tempId = this.generateTempId();
    
    try {
      this.updateMessageStatus(tempId, 'sending');
      
      const response = await fetch(`/api/fan-pages/${pageId}/messages`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          content: content.trim(),
          isReply: true,
          recipientId,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        this.updateMessageStatus(tempId, 'sent');
        return {
          success: true,
          data: result.data,
          message: "Reply sent successfully"
        };
      } else {
        this.updateMessageStatus(tempId, 'failed');
        return {
          success: false,
          error: result.error || "Failed to send reply"
        };
      }
    } catch (error) {
      this.updateMessageStatus(tempId, 'failed');
      this.addToRetryQueue(tempId, { content, fanPageId: pageId, isReply: true, recipientId });
      
      return {
        success: false,
        error: "Network error. Reply will be retried."
      };
    }
  }

  // Mark messages as read
  async markAsRead(conversationId: string): Promise<MessageResponse> {
    try {
      let response;
      
      if (conversationId.startsWith('fanpage_')) {
        const fanPageId = conversationId.replace('fanpage_', '');
        response = await fetch(`/api/fan-pages/${fanPageId}/messages`, {
          method: "PUT",
        });
      } else {
        response = await fetch("/api/messages", {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ senderId: conversationId }),
        });
      }

      if (response.ok) {
        return { success: true, message: "Messages marked as read" };
      } else {
        return { success: false, error: "Failed to mark messages as read" };
      }
    } catch (error) {
      return { success: false, error: "Network error" };
    }
  }

  // Fetch messages for conversation
  async fetchMessages(conversationId: string): Promise<MessageResponse> {
    try {
      const response = await fetch(`/api/messages?userId=${conversationId}`);
      
      if (response.ok) {
        const data = await response.json();
        return { success: true, data };
      } else {
        const error = await response.json();
        return { success: false, error: error.error || "Failed to fetch messages" };
      }
    } catch (error) {
      return { success: false, error: "Network error" };
    }
  }

  // Fetch conversations list
  async fetchConversations(): Promise<MessageResponse> {
    try {
      const response = await fetch("/api/messages");

      if (response.ok) {
        const data = await response.json();
        return { success: true, data };
      } else {
        const error = await response.json();
        return { success: false, error: error.message || "Failed to fetch conversations" };
      }
    } catch (error) {
      return { success: false, error: "Network error" };
    }
  }

  // Real-time message listening
  listenToConversation(
    otherUserId: string,
    onMessage: (messages: FirebaseMessage[]) => void,
    onError?: (error: string) => void
  ): () => void {
    if (!this.isFirebaseInitialized) {
      onError?.('Firebase not initialized');
      return () => {};
    }

    return firebaseMessageService.listenToConversation(otherUserId, onMessage, onError);
  }

  // Listen to typing indicators
  listenToTyping(
    otherUserId: string,
    onTyping: (typingUsers: any[]) => void
  ): () => void {
    if (!this.isFirebaseInitialized) {
      return () => {};
    }

    return firebaseMessageService.listenToTyping(otherUserId, onTyping);
  }

  // Set typing status
  async setTyping(otherUserId: string, isTyping: boolean): Promise<void> {
    if (this.isFirebaseInitialized) {
      await firebaseMessageService.setTyping(otherUserId, isTyping);
    }
  }

  // Listen to real-time notifications
  listenToNotifications(
    onNotification: (notifications: any[]) => void,
    onError?: (error: string) => void
  ): () => void {
    if (!this.isFirebaseInitialized) {
      onError?.('Firebase not initialized');
      return () => {};
    }

    return firebaseNotificationService.listenToNotifications(onNotification, onError);
  }

  // Mark notification as read
  async markNotificationAsRead(notificationId: string): Promise<void> {
    if (this.isFirebaseInitialized) {
      await firebaseNotificationService.markAsRead(notificationId);
    }
  }

  // Retry failed messages
  async retryFailedMessages(): Promise<void> {
    const failedMessages = [...this.retryQueue];
    this.retryQueue = [];

    for (const { id, options } of failedMessages) {
      const status = this.messageStatuses.get(id);
      if (status && status.retryCount && status.retryCount < this.maxRetries) {
        try {
          let result: MessageResponse;
          
          if (options.fanPageId && options.isReply) {
            result = await this.sendPageReply(options.content, options.fanPageId, options.recipientId!);
          } else if (options.fanPageId) {
            result = await this.sendFanPageMessage(options.content, options.fanPageId);
          } else if (options.receiverId) {
            result = await this.sendUserMessage(options.content, options.receiverId);
          } else {
            continue;
          }

          if (!result.success) {
            this.addToRetryQueue(id, options);
          }
        } catch (error) {
          this.addToRetryQueue(id, options);
        }
      }
    }
  }

  // Utility methods
  private generateTempId(): string {
    return `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private updateMessageStatus(id: string, status: MessageStatus['status']): void {
    const existing = this.messageStatuses.get(id);
    this.messageStatuses.set(id, {
      id,
      status,
      timestamp: new Date(),
      retryCount: existing?.retryCount || 0
    });
  }

  private addToRetryQueue(id: string, options: SendMessageOptions): void {
    const status = this.messageStatuses.get(id);
    if (status && (status.retryCount || 0) < this.maxRetries) {
      status.retryCount = (status.retryCount || 0) + 1;
      this.retryQueue.push({ id, options });
      
      // Schedule retry after delay
      setTimeout(() => {
        this.retryFailedMessages();
      }, Math.pow(2, status.retryCount) * 1000); // Exponential backoff
    }
  }

  // Get message status
  getMessageStatus(id: string): MessageStatus | undefined {
    return this.messageStatuses.get(id);
  }

  // Clear old statuses
  clearOldStatuses(): void {
    const now = new Date();
    for (const [id, status] of this.messageStatuses.entries()) {
      if (now.getTime() - status.timestamp.getTime() > 300000) { // 5 minutes
        this.messageStatuses.delete(id);
      }
    }
  }
}

export const messageService = new MessageService();

// Auto-cleanup old statuses every 5 minutes
if (typeof window !== 'undefined') {
  setInterval(() => {
    messageService.clearOldStatuses();
  }, 300000);
}
