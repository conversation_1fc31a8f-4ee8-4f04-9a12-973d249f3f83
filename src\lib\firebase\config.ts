/**
 * Firebase Configuration for Cost-Optimized Real-time Features
 * 
 * This configuration is designed to minimize Firebase costs while
 * providing essential real-time functionality for messaging and notifications.
 */

import { initializeApp, getApps, FirebaseApp } from 'firebase/app';
import { getDatabase, Database, connectDatabaseEmulator } from 'firebase/database';

// Firebase configuration interface
interface FirebaseConfig {
  apiKey: string;
  authDomain: string;
  databaseURL: string;
  projectId: string;
  storageBucket: string;
  messagingSenderId: string;
  appId: string;
}

// Get Firebase configuration from environment variables
const firebaseConfig: FirebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || '',
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || '',
  databaseURL: process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL || '',
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || '',
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || '',
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || '',
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || '',
};

// Validate Firebase configuration
const validateConfig = (config: FirebaseConfig): boolean => {
  const requiredFields = ['apiKey', 'authDomain', 'databaseURL', 'projectId'];
  return requiredFields.every(field => config[field as keyof FirebaseConfig]);
};

// Initialize Firebase app
let app: FirebaseApp;
let database: Database;

if (typeof window !== 'undefined') {
  // Client-side initialization
  if (!validateConfig(firebaseConfig)) {
    console.warn('⚠️ Firebase configuration incomplete. Real-time features will be disabled.');
  } else {
    // Initialize Firebase app if not already initialized
    if (!getApps().length) {
      app = initializeApp(firebaseConfig);
      console.log('🔥 Firebase initialized successfully');
    } else {
      app = getApps()[0];
    }

    // Initialize Realtime Database with cost optimization settings
    database = getDatabase(app);

    // Connect to emulator in development
    if (process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_USE_FIREBASE_EMULATOR === 'true') {
      try {
        connectDatabaseEmulator(database, 'localhost', 9000);
        console.log('🔧 Connected to Firebase Realtime Database emulator');
      } catch (error) {
        console.log('Firebase emulator already connected or not available');
      }
    }
  }
}

// Export Firebase instances
export { app, database };

// Firebase connection status
export const isFirebaseConfigured = (): boolean => {
  return validateConfig(firebaseConfig) && typeof database !== 'undefined';
};

// Cost optimization settings
export const FIREBASE_SETTINGS = {
  // Limit message history to reduce storage costs
  MESSAGE_HISTORY_LIMIT: 50,
  
  // Batch size for loading messages
  MESSAGE_BATCH_SIZE: 20,
  
  // Connection timeout to save bandwidth
  CONNECTION_TIMEOUT: 30000, // 30 seconds
  
  // Offline timeout to reduce unnecessary connections
  OFFLINE_TIMEOUT: 60000, // 1 minute
  
  // Maximum concurrent listeners to prevent excessive reads
  MAX_CONCURRENT_LISTENERS: 5,
  
  // Debounce time for typing indicators
  TYPING_DEBOUNCE_TIME: 1000, // 1 second
  
  // Notification batch size
  NOTIFICATION_BATCH_SIZE: 10,
  
  // Cache duration for user presence
  PRESENCE_CACHE_DURATION: 300000, // 5 minutes
} as const;

// Database paths for organized data structure
export const DB_PATHS = {
  // Messages organized by conversation ID
  MESSAGES: 'messages',
  
  // User conversations with metadata
  CONVERSATIONS: 'conversations',
  
  // User presence status
  PRESENCE: 'presence',
  
  // Typing indicators
  TYPING: 'typing',
  
  // Real-time notifications
  NOTIFICATIONS: 'notifications',
  
  // Connection status
  CONNECTIONS: 'connections',
} as const;

// Helper function to generate conversation ID
export const generateConversationId = (userId1: string, userId2: string): string => {
  return [userId1, userId2].sort().join('_');
};

// Helper function to get user-specific paths
export const getUserPaths = (userId: string) => ({
  conversations: `${DB_PATHS.CONVERSATIONS}/${userId}`,
  presence: `${DB_PATHS.PRESENCE}/${userId}`,
  notifications: `${DB_PATHS.NOTIFICATIONS}/${userId}`,
  connections: `${DB_PATHS.CONNECTIONS}/${userId}`,
});

// Helper function to get conversation-specific paths
export const getConversationPaths = (conversationId: string) => ({
  messages: `${DB_PATHS.MESSAGES}/${conversationId}`,
  typing: `${DB_PATHS.TYPING}/${conversationId}`,
});

// Export configuration for debugging
export const getFirebaseConfig = () => ({
  isConfigured: isFirebaseConfigured(),
  projectId: firebaseConfig.projectId,
  databaseURL: firebaseConfig.databaseURL,
  settings: FIREBASE_SETTINGS,
});

console.log('🔥 Firebase configuration loaded:', {
  configured: isFirebaseConfigured(),
  projectId: firebaseConfig.projectId || 'Not configured',
  environment: process.env.NODE_ENV,
});
