{
  "rules": {
    ".read": false,
    ".write": false,
    
    "messages": {
      "$conversationId": {
        ".read": "auth != null && (
          root.child('conversations').child($conversationId).child('participants').hasChild(auth.uid)
        )",
        ".write": "auth != null && (
          root.child('conversations').child($conversationId).child('participants').hasChild(auth.uid)
        )",
        "$messageId": {
          ".validate": "newData.hasChildren(['senderId', 'receiverId', 'content', 'timestamp', 'status', 'type']) && 
                       newData.child('senderId').val() == auth.uid &&
                       newData.child('content').isString() &&
                       newData.child('content').val().length > 0 &&
                       newData.child('content').val().length <= 5000 &&
                       newData.child('timestamp').isNumber() &&
                       newData.child('status').val().matches(/^(sent|delivered|read)$/) &&
                       newData.child('type').val().matches(/^(text|image|file)$/)"
        }
      }
    },
    
    "conversations": {
      "$conversationId": {
        ".read": "auth != null && data.child('participants').hasChild(auth.uid)",
        ".write": "auth != null && (
          data.child('participants').hasChild(auth.uid) || 
          newData.child('participants').hasChild(auth.uid)
        )",
        ".validate": "newData.hasChildren(['participants', 'lastMessage', 'unreadCount', 'updatedAt']) &&
                     newData.child('participants').hasChildren() &&
                     newData.child('participants').numChildren() == 2 &&
                     newData.child('lastMessage').hasChildren(['content', 'timestamp', 'senderId']) &&
                     newData.child('unreadCount').hasChildren() &&
                     newData.child('updatedAt').isNumber()"
      }
    },
    
    "presence": {
      "$userId": {
        ".read": "auth != null",
        ".write": "auth != null && auth.uid == $userId",
        ".validate": "newData.hasChildren(['online', 'lastSeen']) &&
                     newData.child('online').isBoolean() &&
                     newData.child('lastSeen').isNumber()"
      }
    },
    
    "typing": {
      "$conversationId": {
        ".read": "auth != null && (
          root.child('conversations').child($conversationId).child('participants').hasChild(auth.uid)
        )",
        "$userId": {
          ".write": "auth != null && auth.uid == $userId",
          ".validate": "newData.hasChildren(['isTyping', 'timestamp']) &&
                       newData.child('isTyping').isBoolean() &&
                       newData.child('timestamp').isNumber()"
        }
      }
    },
    
    "notifications": {
      "$userId": {
        ".read": "auth != null && auth.uid == $userId",
        ".write": "auth != null",
        "$notificationId": {
          ".validate": "newData.hasChildren(['type', 'title', 'message', 'read', 'timestamp']) &&
                       newData.child('recipientId').val() == $userId &&
                       newData.child('type').isString() &&
                       newData.child('title').isString() &&
                       newData.child('message').isString() &&
                       newData.child('read').isBoolean() &&
                       newData.child('timestamp').isNumber()"
        }
      }
    },
    
    "user_preferences": {
      "$userId": {
        ".read": "auth != null && auth.uid == $userId",
        ".write": "auth != null && auth.uid == $userId",
        "notifications": {
          ".validate": "newData.hasChildren() &&
                       newData.child('messages').isBoolean() &&
                       newData.child('likes').isBoolean() &&
                       newData.child('comments').isBoolean() &&
                       newData.child('friendRequests').isBoolean() &&
                       newData.child('groupInvites').isBoolean() &&
                       newData.child('fanPageUpdates').isBoolean() &&
                       newData.child('emailNotifications').isBoolean() &&
                       newData.child('pushNotifications').isBoolean()"
        }
      }
    },
    
    "connections": {
      "$userId": {
        ".read": "auth != null && auth.uid == $userId",
        ".write": "auth != null && auth.uid == $userId",
        ".validate": "newData.hasChildren(['isActive', 'lastActivity']) &&
                     newData.child('isActive').isBoolean() &&
                     newData.child('lastActivity').isNumber()"
      }
    },
    
    "usage_stats": {
      ".read": "auth != null && root.child('admin_users').hasChild(auth.uid)",
      ".write": "auth != null && root.child('admin_users').hasChild(auth.uid)"
    },
    
    "admin_users": {
      ".read": "auth != null && data.hasChild(auth.uid)",
      ".write": false
    }
  }
}
