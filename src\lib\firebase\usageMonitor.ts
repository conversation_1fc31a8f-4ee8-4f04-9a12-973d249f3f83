/**
 * Firebase Usage Monitor
 * 
 * Tracks Firebase usage to monitor costs and optimize performance.
 * Provides alerts when usage approaches limits.
 */

import { database, isFirebaseConfigured } from './config';
import { ref, set, get, push, serverTimestamp } from 'firebase/database';

export interface UsageStats {
  reads: number;
  writes: number;
  bandwidth: number; // in bytes
  connections: number;
  timestamp: number;
  period: 'hourly' | 'daily' | 'monthly';
}

export interface UsageAlert {
  type: 'reads' | 'writes' | 'bandwidth' | 'connections' | 'cost';
  threshold: number;
  current: number;
  percentage: number;
  message: string;
  severity: 'warning' | 'critical';
  timestamp: number;
}

export interface UsageLimits {
  dailyReads: number;
  dailyWrites: number;
  dailyBandwidth: number; // in bytes
  monthlyCost: number; // in USD
  concurrentConnections: number;
}

class FirebaseUsageMonitor {
  private stats: UsageStats = {
    reads: 0,
    writes: 0,
    bandwidth: 0,
    connections: 0,
    timestamp: Date.now(),
    period: 'daily',
  };

  private limits: UsageLimits = {
    dailyReads: 50000, // Free tier daily limit
    dailyWrites: 20000, // Free tier daily limit
    dailyBandwidth: 10 * 1024 * 1024 * 1024, // 10GB
    monthlyCost: 25, // $25 monthly budget
    concurrentConnections: 100,
  };

  private alertCallbacks: Array<(alert: UsageAlert) => void> = [];
  private isMonitoring = false;
  private monitoringInterval: NodeJS.Timeout | null = null;

  /**
   * Initialize usage monitoring
   */
  public async initialize(customLimits?: Partial<UsageLimits>): Promise<void> {
    if (!isFirebaseConfigured()) {
      console.warn('Firebase not configured, usage monitoring disabled');
      return;
    }

    if (customLimits) {
      this.limits = { ...this.limits, ...customLimits };
    }

    // Load existing stats
    await this.loadStats();

    // Start monitoring
    this.startMonitoring();

    console.log('📊 Firebase usage monitoring initialized', {
      limits: this.limits,
      currentStats: this.stats,
    });
  }

  /**
   * Track a read operation
   */
  public trackRead(estimatedSize: number = 1024): void {
    this.stats.reads++;
    this.stats.bandwidth += estimatedSize;
    this.checkThresholds();
  }

  /**
   * Track a write operation
   */
  public trackWrite(estimatedSize: number = 1024): void {
    this.stats.writes++;
    this.stats.bandwidth += estimatedSize;
    this.checkThresholds();
  }

  /**
   * Track bandwidth usage
   */
  public trackBandwidth(bytes: number): void {
    this.stats.bandwidth += bytes;
    this.checkThresholds();
  }

  /**
   * Track connection count
   */
  public trackConnection(isConnecting: boolean): void {
    if (isConnecting) {
      this.stats.connections++;
    } else {
      this.stats.connections = Math.max(0, this.stats.connections - 1);
    }
    this.checkThresholds();
  }

  /**
   * Get current usage statistics
   */
  public getStats(): UsageStats {
    return { ...this.stats };
  }

  /**
   * Get usage as percentage of limits
   */
  public getUsagePercentages(): Record<string, number> {
    return {
      reads: (this.stats.reads / this.limits.dailyReads) * 100,
      writes: (this.stats.writes / this.limits.dailyWrites) * 100,
      bandwidth: (this.stats.bandwidth / this.limits.dailyBandwidth) * 100,
      connections: (this.stats.connections / this.limits.concurrentConnections) * 100,
    };
  }

  /**
   * Estimate current cost
   */
  public estimateCost(): { daily: number; monthly: number; breakdown: Record<string, number> } {
    // Firebase pricing (approximate)
    const pricing = {
      readPer100k: 0.36, // $0.36 per 100k reads
      writePer100k: 1.08, // $1.08 per 100k writes
      bandwidthPerGB: 1.00, // $1.00 per GB
    };

    const readCost = (this.stats.reads / 100000) * pricing.readPer100k;
    const writeCost = (this.stats.writes / 100000) * pricing.writePer100k;
    const bandwidthCost = (this.stats.bandwidth / (1024 * 1024 * 1024)) * pricing.bandwidthPerGB;

    const dailyCost = readCost + writeCost + bandwidthCost;
    const monthlyCost = dailyCost * 30; // Rough estimate

    return {
      daily: dailyCost,
      monthly: monthlyCost,
      breakdown: {
        reads: readCost,
        writes: writeCost,
        bandwidth: bandwidthCost,
      },
    };
  }

  /**
   * Add alert callback
   */
  public onAlert(callback: (alert: UsageAlert) => void): () => void {
    this.alertCallbacks.push(callback);
    
    return () => {
      const index = this.alertCallbacks.indexOf(callback);
      if (index > -1) {
        this.alertCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Reset daily stats
   */
  public async resetDailyStats(): Promise<void> {
    this.stats = {
      reads: 0,
      writes: 0,
      bandwidth: 0,
      connections: this.stats.connections, // Keep connection count
      timestamp: Date.now(),
      period: 'daily',
    };

    await this.saveStats();
    console.log('📊 Daily usage stats reset');
  }

  /**
   * Get usage history
   */
  public async getUsageHistory(days: number = 7): Promise<UsageStats[]> {
    if (!isFirebaseConfigured()) return [];

    try {
      const historyRef = ref(database, 'usage_stats');
      const snapshot = await get(historyRef);
      
      if (!snapshot.exists()) return [];

      const history = Object.values(snapshot.val()) as UsageStats[];
      const cutoffTime = Date.now() - (days * 24 * 60 * 60 * 1000);
      
      return history
        .filter(stat => stat.timestamp > cutoffTime)
        .sort((a, b) => b.timestamp - a.timestamp);
    } catch (error) {
      console.error('Error fetching usage history:', error);
      return [];
    }
  }

  /**
   * Export usage data
   */
  public async exportUsageData(): Promise<string> {
    const history = await this.getUsageHistory(30); // Last 30 days
    const currentStats = this.getStats();
    const costEstimate = this.estimateCost();
    const percentages = this.getUsagePercentages();

    const exportData = {
      exportDate: new Date().toISOString(),
      currentStats,
      costEstimate,
      usagePercentages: percentages,
      limits: this.limits,
      history,
    };

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * Cleanup monitoring
   */
  public cleanup(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    
    this.isMonitoring = false;
    this.alertCallbacks = [];
    
    console.log('📊 Usage monitoring cleaned up');
  }

  // Private methods

  private async loadStats(): Promise<void> {
    if (!isFirebaseConfigured()) return;

    try {
      const today = new Date().toISOString().split('T')[0];
      const statsRef = ref(database, `usage_stats/${today}`);
      const snapshot = await get(statsRef);
      
      if (snapshot.exists()) {
        this.stats = { ...this.stats, ...snapshot.val() };
      }
    } catch (error) {
      console.error('Error loading usage stats:', error);
    }
  }

  private async saveStats(): Promise<void> {
    if (!isFirebaseConfigured()) return;

    try {
      const today = new Date().toISOString().split('T')[0];
      const statsRef = ref(database, `usage_stats/${today}`);
      await set(statsRef, this.stats);
    } catch (error) {
      console.error('Error saving usage stats:', error);
    }
  }

  private startMonitoring(): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    
    // Save stats every 5 minutes
    this.monitoringInterval = setInterval(async () => {
      await this.saveStats();
    }, 5 * 60 * 1000);

    // Reset stats daily at midnight
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    
    const msUntilMidnight = tomorrow.getTime() - now.getTime();
    
    setTimeout(() => {
      this.resetDailyStats();
      
      // Set up daily reset interval
      setInterval(() => {
        this.resetDailyStats();
      }, 24 * 60 * 60 * 1000);
    }, msUntilMidnight);
  }

  private checkThresholds(): void {
    const percentages = this.getUsagePercentages();
    const costEstimate = this.estimateCost();

    // Check read threshold
    if (percentages.reads > 80) {
      this.triggerAlert({
        type: 'reads',
        threshold: this.limits.dailyReads,
        current: this.stats.reads,
        percentage: percentages.reads,
        message: `Read operations at ${percentages.reads.toFixed(1)}% of daily limit`,
        severity: percentages.reads > 95 ? 'critical' : 'warning',
        timestamp: Date.now(),
      });
    }

    // Check write threshold
    if (percentages.writes > 80) {
      this.triggerAlert({
        type: 'writes',
        threshold: this.limits.dailyWrites,
        current: this.stats.writes,
        percentage: percentages.writes,
        message: `Write operations at ${percentages.writes.toFixed(1)}% of daily limit`,
        severity: percentages.writes > 95 ? 'critical' : 'warning',
        timestamp: Date.now(),
      });
    }

    // Check bandwidth threshold
    if (percentages.bandwidth > 80) {
      this.triggerAlert({
        type: 'bandwidth',
        threshold: this.limits.dailyBandwidth,
        current: this.stats.bandwidth,
        percentage: percentages.bandwidth,
        message: `Bandwidth usage at ${percentages.bandwidth.toFixed(1)}% of daily limit`,
        severity: percentages.bandwidth > 95 ? 'critical' : 'warning',
        timestamp: Date.now(),
      });
    }

    // Check cost threshold
    const costPercentage = (costEstimate.monthly / this.limits.monthlyCost) * 100;
    if (costPercentage > 80) {
      this.triggerAlert({
        type: 'cost',
        threshold: this.limits.monthlyCost,
        current: costEstimate.monthly,
        percentage: costPercentage,
        message: `Estimated monthly cost at ${costPercentage.toFixed(1)}% of budget ($${costEstimate.monthly.toFixed(2)})`,
        severity: costPercentage > 95 ? 'critical' : 'warning',
        timestamp: Date.now(),
      });
    }
  }

  private triggerAlert(alert: UsageAlert): void {
    // Debounce alerts (don't send same alert within 5 minutes)
    const alertKey = `${alert.type}_${alert.severity}`;
    const lastAlert = this.getLastAlertTime(alertKey);
    
    if (Date.now() - lastAlert < 5 * 60 * 1000) {
      return; // Skip duplicate alert
    }

    this.setLastAlertTime(alertKey, Date.now());
    
    // Trigger callbacks
    this.alertCallbacks.forEach(callback => {
      try {
        callback(alert);
      } catch (error) {
        console.error('Error in alert callback:', error);
      }
    });

    console.warn('🚨 Firebase usage alert:', alert);
  }

  private lastAlertTimes: Map<string, number> = new Map();

  private getLastAlertTime(key: string): number {
    return this.lastAlertTimes.get(key) || 0;
  }

  private setLastAlertTime(key: string, time: number): void {
    this.lastAlertTimes.set(key, time);
  }
}

// Export singleton instance
export const firebaseUsageMonitor = new FirebaseUsageMonitor();

// Export convenience functions
export const initializeUsageMonitoring = (limits?: Partial<UsageLimits>) =>
  firebaseUsageMonitor.initialize(limits);

export const trackFirebaseRead = (size?: number) =>
  firebaseUsageMonitor.trackRead(size);

export const trackFirebaseWrite = (size?: number) =>
  firebaseUsageMonitor.trackWrite(size);

export const getFirebaseUsageStats = () =>
  firebaseUsageMonitor.getStats();

export const onFirebaseUsageAlert = (callback: (alert: UsageAlert) => void) =>
  firebaseUsageMonitor.onAlert(callback);
