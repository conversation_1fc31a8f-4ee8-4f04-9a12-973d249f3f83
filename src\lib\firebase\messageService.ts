/**
 * Firebase Real-time Message Service
 * 
 * Cost-optimized messaging system using Firebase Realtime Database.
 * Implements efficient data structures and bandwidth optimization.
 */

import { database, isFirebaseConfigured, FIREBASE_SETTINGS, generateConversationId, getConversationPaths, getUserPaths } from './config';
import { connectionManager } from './connectionManager';
import { ref, push, set, serverTimestamp, query, limitToLast, orderByKey, get } from 'firebase/database';

export interface FirebaseMessage {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  timestamp: number;
  status: 'sending' | 'sent' | 'delivered' | 'read';
  type: 'text' | 'image' | 'file';
}

export interface ConversationMetadata {
  participants: string[];
  lastMessage: {
    content: string;
    timestamp: number;
    senderId: string;
  };
  unreadCount: { [userId: string]: number };
  updatedAt: number;
}

export interface TypingStatus {
  userId: string;
  isTyping: boolean;
  timestamp: number;
}

class FirebaseMessageService {
  private currentUserId: string | null = null;
  private activeConversations: Set<string> = new Set();
  private typingTimeouts: Map<string, NodeJS.Timeout> = new Map();

  /**
   * Initialize message service for a user
   */
  public async initialize(userId: string): Promise<void> {
    if (!isFirebaseConfigured()) {
      console.warn('Firebase not configured, message service disabled');
      return;
    }

    this.currentUserId = userId;
    await connectionManager.connectUser(userId);
    
    console.log(`💬 Message service initialized for user: ${userId}`);
  }

  /**
   * Send a message with cost optimization
   */
  public async sendMessage(
    receiverId: string,
    content: string,
    type: 'text' | 'image' | 'file' = 'text'
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    if (!this.currentUserId || !isFirebaseConfigured()) {
      return { success: false, error: 'Service not initialized' };
    }

    try {
      const conversationId = generateConversationId(this.currentUserId, receiverId);
      const conversationPaths = getConversationPaths(conversationId);
      
      // Create message object
      const messageData: Omit<FirebaseMessage, 'id'> = {
        senderId: this.currentUserId,
        receiverId,
        content: content.trim(),
        timestamp: Date.now(),
        status: 'sent',
        type,
      };

      // Push message to Firebase
      const messagesRef = ref(database, conversationPaths.messages);
      const newMessageRef = push(messagesRef);
      const messageId = newMessageRef.key!;

      await set(newMessageRef, messageData);

      // Update conversation metadata
      await this.updateConversationMetadata(conversationId, {
        participants: [this.currentUserId, receiverId],
        lastMessage: {
          content: content.trim(),
          timestamp: Date.now(),
          senderId: this.currentUserId,
        },
        unreadCount: {
          [this.currentUserId]: 0,
          [receiverId]: 1, // Increment unread for receiver
        },
        updatedAt: Date.now(),
      });

      console.log(`💬 Message sent: ${messageId}`);
      return { success: true, messageId };
    } catch (error) {
      console.error('Error sending message:', error);
      return { success: false, error: 'Failed to send message' };
    }
  }

  /**
   * Listen to messages in a conversation
   */
  public listenToConversation(
    otherUserId: string,
    onMessage: (messages: FirebaseMessage[]) => void,
    onError?: (error: string) => void
  ): () => void {
    if (!this.currentUserId || !isFirebaseConfigured()) {
      onError?.('Service not initialized');
      return () => {};
    }

    const conversationId = generateConversationId(this.currentUserId, otherUserId);
    const conversationPaths = getConversationPaths(conversationId);
    
    this.activeConversations.add(conversationId);

    // Listen to messages with limit for cost optimization
    connectionManager.addListener(
      `messages_${conversationId}`,
      conversationPaths.messages,
      (data) => {
        if (data) {
          const messages: FirebaseMessage[] = Object.entries(data)
            .map(([id, messageData]: [string, any]) => ({
              id,
              ...messageData,
            }))
            .sort((a, b) => a.timestamp - b.timestamp);
          
          onMessage(messages);
        } else {
          onMessage([]);
        }
      },
      { limitToLast: FIREBASE_SETTINGS.MESSAGE_HISTORY_LIMIT }
    );

    // Return cleanup function
    return () => {
      connectionManager.removeListener(`messages_${conversationId}`);
      this.activeConversations.delete(conversationId);
    };
  }

  /**
   * Listen to typing indicators
   */
  public listenToTyping(
    otherUserId: string,
    onTyping: (typingUsers: TypingStatus[]) => void
  ): () => void {
    if (!this.currentUserId || !isFirebaseConfigured()) {
      return () => {};
    }

    const conversationId = generateConversationId(this.currentUserId, otherUserId);
    const conversationPaths = getConversationPaths(conversationId);

    connectionManager.addListener(
      `typing_${conversationId}`,
      conversationPaths.typing,
      (data) => {
        if (data) {
          const typingUsers: TypingStatus[] = Object.entries(data)
            .map(([userId, typingData]: [string, any]) => ({
              userId,
              ...typingData,
            }))
            .filter((status) => 
              status.userId !== this.currentUserId && 
              status.isTyping &&
              Date.now() - status.timestamp < FIREBASE_SETTINGS.TYPING_DEBOUNCE_TIME * 3
            );
          
          onTyping(typingUsers);
        } else {
          onTyping([]);
        }
      }
    );

    return () => {
      connectionManager.removeListener(`typing_${conversationId}`);
    };
  }

  /**
   * Set typing status
   */
  public async setTyping(otherUserId: string, isTyping: boolean): Promise<void> {
    if (!this.currentUserId || !isFirebaseConfigured()) return;

    const conversationId = generateConversationId(this.currentUserId, otherUserId);
    const conversationPaths = getConversationPaths(conversationId);
    const typingRef = ref(database, `${conversationPaths.typing}/${this.currentUserId}`);

    try {
      if (isTyping) {
        await set(typingRef, {
          isTyping: true,
          timestamp: Date.now(),
        });

        // Auto-clear typing after timeout
        const timeoutKey = `${conversationId}_${this.currentUserId}`;
        if (this.typingTimeouts.has(timeoutKey)) {
          clearTimeout(this.typingTimeouts.get(timeoutKey)!);
        }

        const timeout = setTimeout(async () => {
          await set(typingRef, {
            isTyping: false,
            timestamp: Date.now(),
          });
          this.typingTimeouts.delete(timeoutKey);
        }, FIREBASE_SETTINGS.TYPING_DEBOUNCE_TIME);

        this.typingTimeouts.set(timeoutKey, timeout);
      } else {
        await set(typingRef, {
          isTyping: false,
          timestamp: Date.now(),
        });
      }
    } catch (error) {
      console.error('Error setting typing status:', error);
    }
  }

  /**
   * Mark messages as read
   */
  public async markAsRead(otherUserId: string): Promise<void> {
    if (!this.currentUserId || !isFirebaseConfigured()) return;

    try {
      const conversationId = generateConversationId(this.currentUserId, otherUserId);
      
      // Update conversation metadata to reset unread count
      const conversationRef = ref(database, `conversations/${conversationId}`);
      const snapshot = await get(conversationRef);
      const conversationData = snapshot.val();

      if (conversationData) {
        const updatedUnreadCount = {
          ...conversationData.unreadCount,
          [this.currentUserId]: 0,
        };

        await set(ref(database, `conversations/${conversationId}/unreadCount`), updatedUnreadCount);
      }
    } catch (error) {
      console.error('Error marking messages as read:', error);
    }
  }

  /**
   * Get conversation list for current user
   */
  public async getConversations(): Promise<ConversationMetadata[]> {
    if (!this.currentUserId || !isFirebaseConfigured()) return [];

    try {
      const conversationsRef = ref(database, 'conversations');
      const snapshot = await get(conversationsRef);
      const data = snapshot.val();

      if (!data) return [];

      const conversations: ConversationMetadata[] = [];
      
      Object.entries(data).forEach(([conversationId, conversationData]: [string, any]) => {
        if (conversationData.participants?.includes(this.currentUserId)) {
          conversations.push({
            ...conversationData,
            id: conversationId,
          } as ConversationMetadata & { id: string });
        }
      });

      // Sort by last message timestamp
      return conversations.sort((a, b) => b.updatedAt - a.updatedAt);
    } catch (error) {
      console.error('Error getting conversations:', error);
      return [];
    }
  }

  /**
   * Load more messages for a conversation
   */
  public async loadMoreMessages(
    otherUserId: string,
    beforeTimestamp: number,
    limit: number = FIREBASE_SETTINGS.MESSAGE_BATCH_SIZE
  ): Promise<FirebaseMessage[]> {
    if (!this.currentUserId || !isFirebaseConfigured()) return [];

    try {
      const conversationId = generateConversationId(this.currentUserId, otherUserId);
      const conversationPaths = getConversationPaths(conversationId);
      
      const messagesRef = ref(database, conversationPaths.messages);
      const snapshot = await get(messagesRef);
      const data = snapshot.val();

      if (!data) return [];

      const messages: FirebaseMessage[] = Object.entries(data)
        .map(([id, messageData]: [string, any]) => ({
          id,
          ...messageData,
        }))
        .filter(message => message.timestamp < beforeTimestamp)
        .sort((a, b) => b.timestamp - a.timestamp)
        .slice(0, limit);

      return messages.reverse(); // Return in chronological order
    } catch (error) {
      console.error('Error loading more messages:', error);
      return [];
    }
  }

  /**
   * Cleanup service
   */
  public cleanup(): void {
    // Clear all typing timeouts
    this.typingTimeouts.forEach(timeout => clearTimeout(timeout));
    this.typingTimeouts.clear();

    // Clear active conversations
    this.activeConversations.clear();

    // Disconnect from Firebase
    if (this.currentUserId) {
      connectionManager.disconnectUser();
    }

    this.currentUserId = null;
    console.log('💬 Message service cleaned up');
  }

  // Private methods

  private async updateConversationMetadata(
    conversationId: string,
    metadata: ConversationMetadata
  ): Promise<void> {
    try {
      const conversationRef = ref(database, `conversations/${conversationId}`);
      await set(conversationRef, metadata);
    } catch (error) {
      console.error('Error updating conversation metadata:', error);
    }
  }
}

// Export singleton instance
export const firebaseMessageService = new FirebaseMessageService();

// Export for debugging
export const getMessageServiceStatus = () => ({
  isInitialized: firebaseMessageService['currentUserId'] !== null,
  activeConversations: Array.from(firebaseMessageService['activeConversations']),
  connectionStatus: connectionManager.getStatus(),
});
