/**
 * Firebase Connection Manager
 * 
 * Manages Firebase connections efficiently to minimize costs and optimize performance.
 * Implements smart connection pooling, automatic disconnection, and bandwidth optimization.
 */

import { database, isFirebaseConfigured, FIREBASE_SETTINGS, getUserPaths } from './config';
import { ref, onValue, off, goOffline, goOnline, serverTimestamp, set, onDisconnect } from 'firebase/database';

interface ConnectionListener {
  id: string;
  ref: any;
  callback: (data: any) => void;
  lastActivity: number;
}

class FirebaseConnectionManager {
  private listeners: Map<string, ConnectionListener> = new Map();
  private isOnline: boolean = false;
  private connectionTimeout: NodeJS.Timeout | null = null;
  private offlineTimeout: NodeJS.Timeout | null = null;
  private currentUserId: string | null = null;
  private presenceRef: any = null;

  constructor() {
    if (typeof window !== 'undefined' && isFirebaseConfigured()) {
      this.initializeConnectionManager();
    }
  }

  /**
   * Initialize connection manager with cost optimization
   */
  private initializeConnectionManager(): void {
    // Monitor page visibility to manage connections
    document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    
    // Monitor network status
    window.addEventListener('online', this.handleNetworkOnline.bind(this));
    window.addEventListener('offline', this.handleNetworkOffline.bind(this));
    
    // Cleanup on page unload
    window.addEventListener('beforeunload', this.cleanup.bind(this));
    
    console.log('🔗 Firebase Connection Manager initialized');
  }

  /**
   * Connect user and set up presence
   */
  public async connectUser(userId: string): Promise<void> {
    if (!isFirebaseConfigured()) {
      console.warn('Firebase not configured, skipping connection');
      return;
    }

    this.currentUserId = userId;
    
    try {
      // Go online if offline
      if (!this.isOnline) {
        goOnline(database);
        this.isOnline = true;
      }

      // Set up user presence
      await this.setupUserPresence(userId);
      
      console.log(`👤 User ${userId} connected to Firebase`);
    } catch (error) {
      console.error('Error connecting user:', error);
    }
  }

  /**
   * Disconnect user and cleanup
   */
  public async disconnectUser(): Promise<void> {
    if (!this.currentUserId) return;

    try {
      // Remove user presence
      if (this.presenceRef) {
        await set(this.presenceRef, {
          online: false,
          lastSeen: serverTimestamp(),
        });
      }

      // Clear all listeners
      this.clearAllListeners();
      
      // Schedule offline mode
      this.scheduleOfflineMode();
      
      this.currentUserId = null;
      console.log('👤 User disconnected from Firebase');
    } catch (error) {
      console.error('Error disconnecting user:', error);
    }
  }

  /**
   * Add a listener with automatic cleanup
   */
  public addListener(
    id: string,
    path: string,
    callback: (data: any) => void,
    options: { once?: boolean; limitToLast?: number } = {}
  ): void {
    if (!isFirebaseConfigured()) {
      console.warn('Firebase not configured, skipping listener');
      return;
    }

    // Check listener limit
    if (this.listeners.size >= FIREBASE_SETTINGS.MAX_CONCURRENT_LISTENERS) {
      console.warn('Maximum concurrent listeners reached, removing oldest');
      this.removeOldestListener();
    }

    try {
      let dbRef = ref(database, path);
      
      // Apply query options for cost optimization
      if (options.limitToLast) {
        // Note: limitToLast would be applied here if using query
        // For now, we'll handle this in the callback
      }

      const listener: ConnectionListener = {
        id,
        ref: dbRef,
        callback,
        lastActivity: Date.now(),
      };

      // Remove existing listener if exists
      this.removeListener(id);

      // Add new listener
      onValue(dbRef, (snapshot) => {
        listener.lastActivity = Date.now();
        
        let data = snapshot.val();
        
        // Apply limit if specified
        if (options.limitToLast && data && typeof data === 'object') {
          const entries = Object.entries(data);
          if (entries.length > options.limitToLast) {
            const limited = entries.slice(-options.limitToLast);
            data = Object.fromEntries(limited);
          }
        }
        
        callback(data);
      });

      this.listeners.set(id, listener);
      
      // Ensure we're online for active listeners
      this.ensureOnline();
      
      console.log(`📡 Listener added: ${id} (${this.listeners.size}/${FIREBASE_SETTINGS.MAX_CONCURRENT_LISTENERS})`);
    } catch (error) {
      console.error(`Error adding listener ${id}:`, error);
    }
  }

  /**
   * Remove a specific listener
   */
  public removeListener(id: string): void {
    const listener = this.listeners.get(id);
    if (listener) {
      try {
        off(listener.ref);
        this.listeners.delete(id);
        console.log(`📡 Listener removed: ${id}`);
        
        // Schedule offline mode if no active listeners
        if (this.listeners.size === 0) {
          this.scheduleOfflineMode();
        }
      } catch (error) {
        console.error(`Error removing listener ${id}:`, error);
      }
    }
  }

  /**
   * Clear all listeners
   */
  public clearAllListeners(): void {
    this.listeners.forEach((listener, id) => {
      try {
        off(listener.ref);
      } catch (error) {
        console.error(`Error removing listener ${id}:`, error);
      }
    });
    
    this.listeners.clear();
    console.log('📡 All listeners cleared');
  }

  /**
   * Get connection status
   */
  public getStatus(): {
    isOnline: boolean;
    activeListeners: number;
    currentUser: string | null;
    isConfigured: boolean;
  } {
    return {
      isOnline: this.isOnline,
      activeListeners: this.listeners.size,
      currentUser: this.currentUserId,
      isConfigured: isFirebaseConfigured(),
    };
  }

  /**
   * Force connection cleanup (for debugging)
   */
  public forceCleanup(): void {
    this.cleanup();
  }

  // Private methods

  private async setupUserPresence(userId: string): Promise<void> {
    const userPaths = getUserPaths(userId);
    this.presenceRef = ref(database, userPaths.presence);

    // Set user as online
    await set(this.presenceRef, {
      online: true,
      lastSeen: serverTimestamp(),
      connectedAt: serverTimestamp(),
    });

    // Set up disconnect handler
    onDisconnect(this.presenceRef).set({
      online: false,
      lastSeen: serverTimestamp(),
    });
  }

  private ensureOnline(): void {
    if (!this.isOnline && isFirebaseConfigured()) {
      goOnline(database);
      this.isOnline = true;
      console.log('🔗 Firebase connection restored');
    }

    // Clear offline timeout
    if (this.offlineTimeout) {
      clearTimeout(this.offlineTimeout);
      this.offlineTimeout = null;
    }
  }

  private scheduleOfflineMode(): void {
    if (this.offlineTimeout) {
      clearTimeout(this.offlineTimeout);
    }

    this.offlineTimeout = setTimeout(() => {
      if (this.listeners.size === 0 && this.isOnline) {
        goOffline(database);
        this.isOnline = false;
        console.log('🔗 Firebase connection paused (no active listeners)');
      }
    }, FIREBASE_SETTINGS.OFFLINE_TIMEOUT);
  }

  private removeOldestListener(): void {
    let oldestId = '';
    let oldestTime = Date.now();

    this.listeners.forEach((listener, id) => {
      if (listener.lastActivity < oldestTime) {
        oldestTime = listener.lastActivity;
        oldestId = id;
      }
    });

    if (oldestId) {
      this.removeListener(oldestId);
    }
  }

  private handleVisibilityChange(): void {
    if (document.hidden) {
      // Page is hidden, schedule cleanup
      this.scheduleOfflineMode();
    } else {
      // Page is visible, ensure connection
      if (this.listeners.size > 0) {
        this.ensureOnline();
      }
    }
  }

  private handleNetworkOnline(): void {
    console.log('🌐 Network online');
    if (this.listeners.size > 0) {
      this.ensureOnline();
    }
  }

  private handleNetworkOffline(): void {
    console.log('🌐 Network offline');
    this.isOnline = false;
  }

  private cleanup(): void {
    this.clearAllListeners();
    
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
    }
    
    if (this.offlineTimeout) {
      clearTimeout(this.offlineTimeout);
    }

    if (this.isOnline) {
      goOffline(database);
      this.isOnline = false;
    }
    
    console.log('🧹 Firebase Connection Manager cleanup completed');
  }
}

// Export singleton instance
export const connectionManager = new FirebaseConnectionManager();

// Export for debugging
export const getConnectionStatus = () => connectionManager.getStatus();
